<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\SsoMobileLoginRequest;
use App\Models\Passport\Client;
use App\Models\User;
use App\Traits\LastLogInLocationTrait;
use App\Traits\MailerLiteRegisterTrait;
use App\Traits\Passport\PassportRedirectTrait;
use App\Traits\UserAlreadyLoginTrait;
use App\Traits\UserLogoutTrait;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class SocialiteController extends Controller
{
    use PassportRedirectTrait;
    use UserLogoutTrait;
    use UserAlreadyLoginTrait;
    use MailerLiteRegisterTrait;
    use LastLogInLocationTrait;

    /**
     * Redirect the user to the Socialite authentication page for the given driver.
     *
     * @param  string  $driver
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function redirect(string $driver)
    {
        return Socialite::driver($driver)->redirect();
    }

    /**
     * Obtain the user information from the given Socialite driver.
     *
     * @param  string  $driver
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function callback(string $driver)
    {
        $authUser = Socialite::driver($driver)->user();

        $user = User::query()->firstOrCreate([
            'email' => $authUser->email,
        ], [
            "{$driver}_id" => $authUser->id,
            'name' => $authUser->name,
            'email' => $authUser->email,
            "{$driver}_token" => $authUser->token,
            "{$driver}_refresh_token" => $authUser->refreshToken,
            'verified' => 1,
            'verified_at' => now(),
            'email_verified_at' => now(),
        ]);

        $this->registerMailerLite($user);

        Auth::login($user, true);
        $this->updateLastLogInLocation();

        if ($this->isUserAlreadyLogin()) {
            $redirectTo = Session::get('redirect_uri');

            $this->revokeAll();

            Session::put('redirect_uri', $redirectTo);

            return to_route('login')->withErrors(['is_already_logged_in' => true])->with(['email' => $authUser->email]);
        }

        createInitSubscription();

        if (request()->session()->has('authRequest')) {
            $authRequest = request()->session()->pull('authRequest');

            // Build the authorization URL
            $query = http_build_query($authRequest);
            return redirect('/oauth/authorize?' . $query);
        }

        // Check if we have an authorization request in the session
        if (session()->has('redirect_uri')) {
            return redirect(Session::get('redirect_uri'));
        }

        return to_route('home');
    }
    /**
     * Handle social login tokens from mobile apps
     *
     * @param  \Illuminate\Http\Request $request
     * @param  string                   $driver
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function mobileSsoLogin(SsoMobileLoginRequest $request, string $driver)
    {
        $socialUser = Socialite::driver($driver)
            ->stateless()
            ->userFromToken($request->access_token);

        $user = User::where('email', $socialUser->getEmail())->first();

        if ( ! $user) {
            $user = User::create([
                'name' => $socialUser->getName(),
                'email' => $socialUser->getEmail(),
                'password' => Hash::make(Str::random(16)),
                'provider' => $request->provider,
                'provider_id' => $socialUser->getId(),
                'verified' => 1,
                'verified_at' => now(),
                'email_verified_at' => now(),
            ]);
        }

        $client = Client::where('personal_access_client', true)->first();
        $response = Http::post(route('passport.token'), [
            'grant_type' => 'personal_access',
            'client_id' => $client->id,
            'client_secret' => $client->secret,
            'user_id' => $user->id,
            'scope' => '',
        ])->json();

        return $this->successResponse($response);
    }
}
