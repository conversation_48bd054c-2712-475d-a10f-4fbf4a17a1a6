<?php

namespace App\Http\Controllers\Auth;

use App\Traits\Passport\PassportSessionTrait;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Http\Request;
use Lara<PERSON>\Passport\Bridge\User;
use <PERSON><PERSON>\Passport\ClientRepository;
use <PERSON><PERSON>\Passport\Contracts\AuthorizationViewResponse;
use <PERSON><PERSON>\Passport\Http\Controllers\AuthorizationController as PassportAuthorizationController;
use <PERSON><PERSON>\Passport\TokenRepository;
use League\OAuth2\Server\AuthorizationServer;
use Nyholm\Psr7\Response as Psr7Response;
use Psr\Http\Message\ServerRequestInterface;

class AuthorizationController extends PassportAuthorizationController
{
    use PassportSessionTrait;

    /**
     * Create a new controller instance.
     *
     * @param  \League\OAuth2\Server\AuthorizationServer  $server
     * @param  \Illuminate\Contracts\Auth\StatefulGuard  $guard
     * @param  \Laravel\Passport\Contracts\AuthorizationViewResponse  $response
     * @return void
     */
    public function __construct(
        AuthorizationServer $server,
        StatefulGuard $guard,
        AuthorizationViewResponse $response,
    ) {
        $this->server = $server;
        $this->guard = $guard;
        $this->response = $response;
    }


    /**
     * Authorize a client to access the user's account.
     *
     * @param  \Psr\Http\Message\ServerRequestInterface  $psrRequest
     * @param  \Illuminate\Http\Request  $request
     * @param  \Laravel\Passport\ClientRepository  $clients
     * @param  \Laravel\Passport\TokenRepository  $tokens
     * @return \Illuminate\Http\Response|\Laravel\Passport\Contracts\AuthorizationViewResponse
     */
    #[\Override]
    public function authorize(
        ServerRequestInterface $psrRequest,
        Request $request,
        ClientRepository $clients,
        TokenRepository $tokens,
    ) {
        // Check if this is a registration flow and logout existing user if needed
        $this->handleRegistrationLogout($request);

        // Instead of flush(), which destroys the session completely,
        // use forget() to clear specific keys or regenerate() to create a new session ID
        session()->regenerate(true); // true means delete the old session
        session()->put('is_first_time', true);

        return parent::authorize($psrRequest, $request, $clients, $tokens);
    }

    /**
     * Handle automatic logout for registration flows.
     *
     * If the OAuth authorization request contains 'is_register=1' parameter
     * and a user is currently authenticated, log out that user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    protected function handleRegistrationLogout(Request $request): void
    {
        // Check if the request contains the is_register parameter set to 1 AND a user is authenticated
        if ($request->has('is_register') && $request->get('is_register') == '1' && $this->guard->check()) {
            // Log out the current user
            $this->guard->logout();
        }
    }

    /**
     * Approve the authorization request.
     *
     * @param \League\OAuth2\Server\RequestTypes\AuthorizationRequest $authRequest
     * @param \Illuminate\Contracts\Auth\Authenticatable              $user
     *
     * @return \Illuminate\Http\Response
     */
    #[\Override]
    protected function approveRequest($authRequest, $user)
    {
        $authRequest->setUser(new User($user->getAuthIdentifier()));

        $authRequest->setAuthorizationApproved(true);

        return $this->withErrorHandling(function () use ($authRequest) {
            $response = $this->convertResponse(
                $this->server->completeAuthorizationRequest($authRequest, new Psr7Response()),
            );

            $this->storeAgent($response);

            return $response;
        });
    }
}
